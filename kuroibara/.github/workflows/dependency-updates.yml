name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Update Python dependencies
        run: |
          cd backend
          pip install --upgrade pip pip-tools pur
          # Use pur to update version ranges in requirements.txt
          if [ -f "requirements.txt" ]; then
            # Update version ranges to latest available versions
            pur -r requirements.txt --dry-run > pur_output.txt || true
            if [ -s pur_output.txt ]; then
              pur -r requirements.txt
              echo "Updated Python dependencies in requirements.txt"
              cat pur_output.txt
            else
              echo "No Python dependency updates available"
            fi
            rm -f pur_output.txt
          else
            echo "No requirements.txt found, skipping Python dependency updates"
          fi

      - name: Update Node.js dependencies
        run: |
          cd frontend/app
          npm update
          npm audit fix || true

      - name: Delete existing dependency update branch
        run: |
          git push origin --delete chore/dependency-updates || echo "Branch doesn't exist or already deleted"
        continue-on-error: true

      - name: Check for changes
        id: changes
        run: |
          if git diff --quiet; then
            echo "No changes detected"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected"
            echo "has_changes=true" >> $GITHUB_OUTPUT
          fi

      - name: Create Pull Request
        if: steps.changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'chore: Automated dependency updates'
          body: |
            This PR contains automated dependency updates.
            
            ## Changes
            - Updated Python dependencies in backend/
            - Updated Node.js dependencies in frontend/app/
            
            Please review the changes and ensure all tests pass before merging.
          branch: chore/dependency-updates
          delete-branch: true
          labels: |
            dependencies
            automated
            chore

      - name: No changes detected
        if: steps.changes.outputs.has_changes == 'false'
        run: |
          echo "No dependency updates available at this time."
          echo "All dependencies are up to date."
